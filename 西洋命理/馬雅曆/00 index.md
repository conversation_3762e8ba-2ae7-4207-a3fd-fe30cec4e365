# 馬雅曆知識體系

## 概述
馬雅曆是古代馬雅文明發展出的複雜曆法系統，結合了天文觀測、數學計算和精神哲學。現代的馬雅曆解讀主要基於José Argüelles的《夢法》(Dreamspell)系統，將古老智慧應用於個人成長和靈性發展。

## 知識架構

### 01 基礎概念
- [[01 馬雅曆基礎概念]]
- 曆法起源與發展
- 現代應用與詮釋
- 基本術語解釋

### 02 曆法系統
- [[02 曆法系統總覽]]
- 260天神聖曆(T<PERSON>lkin)
- 365天太陽曆(Haab)
- 長計曆系統

### 03 二十個太陽圖騰
- [[03 太陽圖騰總覽]]
- 20個太陽圖騰詳解
- 圖騰特質與能量

### 04 十三個調性
- [[04 調性系統]]
- 13個調性意義
- 調性與圖騰的組合

### 05 波符系統
- [[05 波符系統]]
- 20個波符週期
- 波符能量流動

### 06 城堡與色彩
- [[06 城堡色彩系統]]
- 四個城堡
- 四種顏色能量

### 07 解讀方法
- [[07 解讀技法]]
- 個人印記解讀
- 流年運勢分析
- 關係合盤技巧

### 08 實用工具
- [[08 實用工具]]
- 計算方法
- 查詢表格
- 常用公式

### 09 Python資源
- [[09 Python程式庫資源]]
- 馬雅曆計算程式庫
- 自動化解讀工具
- 資料視覺化

## 學習建議
1. 從基礎概念開始，理解馬雅曆的核心思想
2. 熟悉20個太陽圖騰和13個調性的意義
3. 學習計算個人印記的方法
4. 練習解讀技巧，從自己開始
5. 運用Python工具提升效率

