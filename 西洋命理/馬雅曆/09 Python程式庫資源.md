# Python程式庫資源

## 概述
本文件提供馬雅曆相關的Python程式庫、工具和程式碼範例，幫助開發者建立自動化的馬雅曆計算和分析工具。

## 核心程式庫

### 1. PyMayan - 馬雅曆計算庫
```python
# 安裝
pip install pymayan

# 基本使用
from pymayan import MayanCalendar
from datetime import date

# 建立馬雅曆實例
maya = MayanCalendar()

# 計算生日Kin
birthday = date(1990, 3, 15)
kin = maya.get_kin(birthday)
print(f"Kin: {kin.number}, {kin.tone} {kin.seal}")
```

### 2. Dreamspell - 夢法系統庫
```python
# 安裝
pip install dreamspell

# 基本使用
from dreamspell import Dreamspell
from datetime import datetime

ds = Dreamspell()
today = datetime.now().date()
daily_kin = ds.calculate_kin(today)
print(f"今日Kin: {daily_kin}")
```

### 3. <PERSON><PERSON><PERSON><PERSON> - 神聖曆庫
```python
# 安裝
pip install tzolkin

# 基本使用
from tzolkin import TzolkinCalendar

tzolkin = TzolkinCalendar()
kin_info = tzolkin.get_kin_info(150)
print(f"圖騰: {kin_info.seal}, 調性: {kin_info.tone}")
```

## 自製馬雅曆計算類

### 基礎計算類
```python
from datetime import date, timedelta
from typing import NamedTuple

class KinInfo(NamedTuple):
    number: int
    seal: str
    tone: int
    color: str
    family: str

class MayanCalculator:
    # 20個太陽圖騰
    SEALS = [
        "紅龍", "白風", "藍夜", "黃種子", "紅蛇",
        "白世界橋", "藍手", "黃星星", "紅月", "白狗",
        "藍猴", "黃人", "紅天行者", "白巫師", "藍鷹",
        "黃戰士", "紅地球", "白鏡", "藍風暴", "黃太陽"
    ]
    
    # 13個調性
    TONES = [
        "磁性", "月亮", "電力", "自我存在", "超頻",
        "韻律", "共鳴", "銀河", "太陽", "行星",
        "光譜", "水晶", "宇宙"
    ]
    
    # 四種顏色
    COLORS = ["紅", "白", "藍", "黃"]
    
    # 五個家族
    FAMILIES = ["極性", "核心", "門戶", "核心", "紅心"]
    
    def __init__(self):
        # 馬雅新年基準點 (2023年7月26日為Kin 1)
        self.base_date = date(2023, 7, 26)
        self.base_kin = 1
    
    def calculate_kin(self, target_date: date) -> KinInfo:
        """計算指定日期的Kin"""
        days_diff = (target_date - self.base_date).days
        kin_number = ((days_diff % 260) + self.base_kin - 1) % 260 + 1
        
        seal_index = (kin_number - 1) % 20
        tone_index = (kin_number - 1) % 13
        color_index = seal_index % 4
        family_index = seal_index % 5
        
        return KinInfo(
            number=kin_number,
            seal=self.SEALS[seal_index],
            tone=tone_index + 1,
            color=self.COLORS[color_index],
            family=self.FAMILIES[family_index]
        )
    
    def get_support_kin(self, kin_number: int) -> int:
        """計算支持Kin"""
        return 261 - kin_number if kin_number <= 260 else kin_number
    
    def get_challenge_kin(self, kin_number: int) -> int:
        """計算挑戰Kin"""
        seal_index = (kin_number - 1) % 20
        tone_index = (kin_number - 1) % 13
        
        # 挑戰圖騰計算 (簡化版)
        challenge_seal = (seal_index + 10) % 20
        challenge_kin = challenge_seal + 1 + (tone_index * 20)
        return ((challenge_kin - 1) % 260) + 1
    
    def get_wavespell(self, kin_number: int) -> dict:
        """計算所在波符資訊"""
        wavespell_number = ((kin_number - 1) // 13) + 1
        position_in_wavespell = ((kin_number - 1) % 13) + 1
        wavespell_seal_index = ((wavespell_number - 1) % 20)
        
        return {
            "number": wavespell_number,
            "seal": self.SEALS[wavespell_seal_index],
            "position": position_in_wavespell,
            "tone": self.TONES[position_in_wavespell - 1]
        }
    
    def get_castle(self, kin_number: int) -> dict:
        """計算所在城堡資訊"""
        castle_names = ["紅色東方", "白色北方", "藍色西方", "黃色南方", "綠色中央"]
        castle_themes = ["誕生", "精煉", "燃燒", "給予", "魔法"]
        
        castle_number = ((kin_number - 1) // 52) + 1
        position_in_castle = ((kin_number - 1) % 52) + 1
        
        return {
            "number": castle_number,
            "name": castle_names[castle_number - 1],
            "theme": castle_themes[castle_number - 1],
            "position": position_in_castle
        }

# 使用範例
calculator = MayanCalculator()
birthday = date(1990, 3, 15)
kin_info = calculator.calculate_kin(birthday)
print(f"生日Kin: {kin_info.number} - {kin_info.tone} {kin_info.seal}")
```

### 進階分析類
```python
class MayanAnalyzer(MayanCalculator):
    def __init__(self):
        super().__init__()
        
    def analyze_person(self, birth_date: date) -> dict:
        """完整的個人分析"""
        kin_info = self.calculate_kin(birth_date)
        support_kin = self.get_support_kin(kin_info.number)
        challenge_kin = self.get_challenge_kin(kin_info.number)
        wavespell = self.get_wavespell(kin_info.number)
        castle = self.get_castle(kin_info.number)
        
        return {
            "birth_kin": kin_info,
            "support_kin": support_kin,
            "challenge_kin": challenge_kin,
            "wavespell": wavespell,
            "castle": castle,
            "characteristics": self.get_characteristics(kin_info)
        }
    
    def get_characteristics(self, kin_info: KinInfo) -> dict:
        """獲取Kin特質描述"""
        seal_traits = {
            "紅龍": "滋養、母性、生命力",
            "白風": "溝通、精神、呼吸",
            "藍夜": "豐盛、夢想、直覺",
            "黃種子": "潛能、成長、覺知",
            # ... 其他圖騰特質
        }
        
        tone_traits = {
            1: "磁性 - 吸引目的",
            2: "月亮 - 極化挑戰",
            3: "電力 - 啟動服務",
            # ... 其他調性特質
        }
        
        return {
            "seal_trait": seal_traits.get(kin_info.seal, "未定義"),
            "tone_trait": tone_traits.get(kin_info.tone, "未定義"),
            "color_energy": f"{kin_info.color}色能量",
            "family_group": f"{kin_info.family}家族"
        }
    
    def relationship_analysis(self, date1: date, date2: date) -> dict:
        """關係分析"""
        person1 = self.analyze_person(date1)
        person2 = self.analyze_person(date2)
        
        # 計算關係類型
        kin1 = person1["birth_kin"].number
        kin2 = person2["birth_kin"].number
        
        relationship_type = "一般關係"
        if kin1 == kin2:
            relationship_type = "相同Kin - 深度共鳴"
        elif kin1 == person2["support_kin"]:
            relationship_type = "支持關係 - 互相支持"
        elif kin1 == person2["challenge_kin"]:
            relationship_type = "挑戰關係 - 成長機會"
        
        return {
            "person1": person1,
            "person2": person2,
            "relationship_type": relationship_type,
            "compatibility_score": self.calculate_compatibility(person1, person2)
        }
    
    def calculate_compatibility(self, person1: dict, person2: dict) -> float:
        """計算相容性分數 (簡化版)"""
        score = 0.5  # 基礎分數
        
        # 顏色相容性
        if person1["birth_kin"].color == person2["birth_kin"].color:
            score += 0.2
        
        # 家族相容性
        if person1["birth_kin"].family == person2["birth_kin"].family:
            score += 0.1
        
        # 調性和諧
        tone_diff = abs(person1["birth_kin"].tone - person2["birth_kin"].tone)
        if tone_diff <= 2:
            score += 0.2
        
        return min(score, 1.0)

# 使用範例
analyzer = MayanAnalyzer()
person_analysis = analyzer.analyze_person(date(1990, 3, 15))
print(f"個人分析: {person_analysis}")
```

## 資料視覺化工具

### 使用Matplotlib繪製馬雅曆圖表
```python
import matplotlib.pyplot as plt
import numpy as np
from datetime import date, timedelta

class MayanVisualizer:
    def __init__(self, calculator: MayanCalculator):
        self.calculator = calculator
        
    def plot_yearly_kin_cycle(self, year: int):
        """繪製年度Kin週期圖"""
        start_date = date(year, 1, 1)
        dates = [start_date + timedelta(days=i) for i in range(365)]
        kins = [self.calculator.calculate_kin(d).number for d in dates]
        
        plt.figure(figsize=(12, 6))
        plt.plot(range(365), kins, linewidth=1)
        plt.title(f'{year}年馬雅曆Kin週期')
        plt.xlabel('天數')
        plt.ylabel('Kin號碼')
        plt.grid(True, alpha=0.3)
        plt.show()
    
    def plot_seal_distribution(self, start_date: date, days: int):
        """繪製圖騰分佈圓餅圖"""
        dates = [start_date + timedelta(days=i) for i in range(days)]
        seals = [self.calculator.calculate_kin(d).seal for d in dates]
        
        seal_counts = {}
        for seal in seals:
            seal_counts[seal] = seal_counts.get(seal, 0) + 1
        
        plt.figure(figsize=(10, 8))
        plt.pie(seal_counts.values(), labels=seal_counts.keys(), autopct='%1.1f%%')
        plt.title(f'{days}天內圖騰分佈')
        plt.show()
    
    def plot_wavespell_calendar(self, year: int, month: int):
        """繪製月度波符日曆"""
        import calendar
        
        # 獲取該月的所有日期
        cal = calendar.monthcalendar(year, month)
        
        fig, ax = plt.subplots(figsize=(12, 8))
        
        for week_num, week in enumerate(cal):
            for day_num, day in enumerate(week):
                if day == 0:
                    continue
                    
                current_date = date(year, month, day)
                kin_info = self.calculator.calculate_kin(current_date)
                wavespell = self.calculator.get_wavespell(kin_info.number)
                
                # 根據顏色設定背景色
                color_map = {"紅": "red", "白": "lightgray", "藍": "blue", "黃": "yellow"}
                bg_color = color_map.get(kin_info.color, "white")
                
                ax.add_patch(plt.Rectangle((day_num, 5-week_num), 1, 1, 
                                         facecolor=bg_color, alpha=0.3))
                ax.text(day_num+0.5, 5-week_num+0.7, str(day), 
                       ha='center', va='center', fontsize=12, fontweight='bold')
                ax.text(day_num+0.5, 5-week_num+0.3, f"Kin {kin_info.number}", 
                       ha='center', va='center', fontsize=8)
        
        ax.set_xlim(0, 7)
        ax.set_ylim(0, 6)
        ax.set_xticks(range(7))
        ax.set_xticklabels(['一', '二', '三', '四', '五', '六', '日'])
        ax.set_yticks([])
        ax.set_title(f'{year}年{month}月馬雅曆')
        plt.tight_layout()
        plt.show()

# 使用範例
calculator = MayanCalculator()
visualizer = MayanVisualizer(calculator)
visualizer.plot_yearly_kin_cycle(2024)
```

### 使用Plotly建立互動式圖表
```python
import plotly.graph_objects as go
import plotly.express as px
from datetime import date, timedelta

class InteractiveMayanViz:
    def __init__(self, calculator: MayanCalculator):
        self.calculator = calculator
    
    def create_kin_timeline(self, start_date: date, days: int):
        """建立互動式Kin時間軸"""
        dates = [start_date + timedelta(days=i) for i in range(days)]
        kin_data = []
        
        for d in dates:
            kin_info = self.calculator.calculate_kin(d)
            wavespell = self.calculator.get_wavespell(kin_info.number)
            kin_data.append({
                'date': d,
                'kin': kin_info.number,
                'seal': kin_info.seal,
                'tone': kin_info.tone,
                'color': kin_info.color,
                'wavespell': wavespell['number']
            })
        
        fig = go.Figure()
        
        # 按顏色分組繪製
        colors = {'紅': 'red', '白': 'lightgray', '藍': 'blue', '黃': 'gold'}
        
        for color in colors:
            color_data = [d for d in kin_data if d['color'] == color]
            if color_data:
                fig.add_trace(go.Scatter(
                    x=[d['date'] for d in color_data],
                    y=[d['kin'] for d in color_data],
                    mode='markers',
                    name=f'{color}色',
                    marker=dict(color=colors[color], size=8),
                    hovertemplate='<b>%{text}</b><br>Kin: %{y}<br>日期: %{x}<extra></extra>',
                    text=[f"{d['tone']} {d['seal']}" for d in color_data]
                ))
        
        fig.update_layout(
            title='馬雅曆Kin時間軸',
            xaxis_title='日期',
            yaxis_title='Kin號碼',
            hovermode='closest'
        )
        
        fig.show()
    
    def create_seal_radar_chart(self, birth_dates: list):
        """建立圖騰雷達圖"""
        seal_counts = {}
        
        for birth_date in birth_dates:
            kin_info = self.calculator.calculate_kin(birth_date)
            seal = kin_info.seal
            seal_counts[seal] = seal_counts.get(seal, 0) + 1
        
        seals = list(seal_counts.keys())
        counts = list(seal_counts.values())
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatterpolar(
            r=counts,
            theta=seals,
            fill='toself',
            name='圖騰分佈'
        ))
        
        fig.update_layout(
            polar=dict(
                radialaxis=dict(
                    visible=True,
                    range=[0, max(counts)]
                )),
            showlegend=False,
            title="圖騰分佈雷達圖"
        )
        
        fig.show()

# 使用範例
calculator = MayanCalculator()
interactive_viz = InteractiveMayanViz(calculator)
interactive_viz.create_kin_timeline(date(2024, 1, 1), 100)
```

## Web應用開發

### 使用Flask建立Web API
```python
from flask import Flask, jsonify, request
from datetime import datetime

app = Flask(__name__)
calculator = MayanCalculator()

@app.route('/api/kin/<date_str>')
def get_kin(date_str):
    """獲取指定日期的Kin資訊"""
    try:
        target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        kin_info = calculator.calculate_kin(target_date)
        
        return jsonify({
            'date': date_str,
            'kin': kin_info.number,
            'seal': kin_info.seal,
            'tone': kin_info.tone,
            'color': kin_info.color,
            'family': kin_info.family
        })
    except ValueError:
        return jsonify({'error': '日期格式錯誤，請使用YYYY-MM-DD'}), 400

@app.route('/api/analysis', methods=['POST'])
def analyze_person():
    """個人分析API"""
    data = request.json
    birth_date_str = data.get('birth_date')
    
    try:
        birth_date = datetime.strptime(birth_date_str, '%Y-%m-%d').date()
        analyzer = MayanAnalyzer()
        analysis = analyzer.analyze_person(birth_date)
        
        return jsonify(analysis)
    except Exception as e:
        return jsonify({'error': str(e)}), 400

@app.route('/api/compatibility', methods=['POST'])
def check_compatibility():
    """關係相容性分析API"""
    data = request.json
    date1_str = data.get('date1')
    date2_str = data.get('date2')
    
    try:
        date1 = datetime.strptime(date1_str, '%Y-%m-%d').date()
        date2 = datetime.strptime(date2_str, '%Y-%m-%d').date()
        
        analyzer = MayanAnalyzer()
        relationship = analyzer.relationship_analysis(date1, date2)
        
        return jsonify(relationship)
    except Exception as e:
        return jsonify({'error': str(e)}), 400

if __name__ == '__main__':
    app.run(debug=True)
```

### 使用Streamlit建立互動式應用
```python
import streamlit as st
from datetime import date, datetime

st.title('馬雅曆分析工具')

# 側邊欄選項
analysis_type = st.sidebar.selectbox(
    '選擇分析類型',
    ['個人分析', '關係分析', '每日Kin', '年度概覽']
)

calculator = MayanCalculator()

if analysis_type == '個人分析':
    st.header('個人馬雅曆分析')
    
    birth_date = st.date_input('選擇生日', value=date(1990, 1, 1))
    
    if st.button('分析'):
        analyzer = MayanAnalyzer()
        analysis = analyzer.analyze_person(birth_date)
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader('基本資訊')
            kin_info = analysis['birth_kin']
            st.write(f"**Kin號碼**: {kin_info.number}")
            st.write(f"**太陽圖騰**: {kin_info.seal}")
            st.write(f"**調性**: {kin_info.tone}")
            st.write(f"**顏色**: {kin_info.color}")
            st.write(f"**家族**: {kin_info.family}")
        
        with col2:
            st.subheader('支持系統')
            st.write(f"**支持Kin**: {analysis['support_kin']}")
            st.write(f"**挑戰Kin**: {analysis['challenge_kin']}")
            
            wavespell = analysis['wavespell']
            st.write(f"**所在波符**: {wavespell['seal']}波符")
            st.write(f"**波符位置**: 第{wavespell['position']}天")

elif analysis_type == '每日Kin':
    st.header('每日Kin查詢')
    
    target_date = st.date_input('選擇日期', value=date.today())
    
    kin_info = calculator.calculate_kin(target_date)
    
    st.success(f"**{target_date}** 的Kin是：**{kin_info.number} - {kin_info.tone} {kin_info.seal}**")
    
    # 顯示詳細資訊
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Kin號碼", kin_info.number)
    with col2:
        st.metric("調性", kin_info.tone)
    with col3:
        st.metric("顏色", kin_info.color)

# 運行應用：streamlit run mayan_app.py
```

## 資料庫整合

### SQLite資料庫設計
```python
import sqlite3
from datetime import date

class MayanDatabase:
    def __init__(self, db_path='mayan_calendar.db'):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """初始化資料庫"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 建立Kin資訊表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS kin_info (
                kin_number INTEGER PRIMARY KEY,
                seal_name TEXT NOT NULL,
                tone_number INTEGER NOT NULL,
                color TEXT NOT NULL,
                family TEXT NOT NULL,
                wavespell_number INTEGER,
                castle_number INTEGER
            )
        ''')
        
        # 建立個人記錄表
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS person_records (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                birth_date DATE NOT NULL,
                kin_number INTEGER,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (kin_number) REFERENCES kin_info (kin_number)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def populate_kin_data(self):
        """填充Kin基礎資料"""
        calculator = MayanCalculator()
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for kin_num in range(1, 261):
            # 這裡需要根據Kin號碼計算對應的圖騰、調性等資訊
            # 簡化版本，實際需要完整的計算邏輯
            seal_index = (kin_num - 1) % 20
            tone_num = ((kin_num - 1) % 13) + 1
            
            cursor.execute('''
                INSERT OR REPLACE INTO kin_info 
                (kin_number, seal_name, tone_number, color, family, wavespell_number, castle_number)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                kin_num,
                calculator.SEALS[seal_index],
                tone_num,
                calculator.COLORS[seal_index % 4],
                calculator.FAMILIES[seal_index % 5],
                ((kin_num - 1) // 13) + 1,
                ((kin_num - 1) // 52) + 1
            ))
        
        conn.commit()
        conn.close()
    
    def add_person(self, name: str, birth_date: date, notes: str = ''):
        """新增個人記錄"""
        calculator = MayanCalculator()
        kin_info = calculator.calculate_kin(birth_date)
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO person_records (name, birth_date, kin_number, notes)
            VALUES (?, ?, ?, ?)
        ''', (name, birth_date, kin_info.number, notes))
        
        conn.commit()
        conn.close()
    
    def get_person_analysis(self, person_id: int):
        """獲取個人分析"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT p.name, p.birth_date, k.seal_name, k.tone_number, k.color, k.family
            FROM person_records p
            JOIN kin_info k ON p.kin_number = k.kin_number
            WHERE p.id = ?
        ''', (person_id,))
        
        result = cursor.fetchone()
        conn.close()
        
        return result

# 使用範例
db = MayanDatabase()
db.populate_kin_data()
db.add_person("張三", date(1990, 3, 15), "測試用戶")
```

## 測試與驗證

### 單元測試
```python
import unittest
from datetime import date

class TestMayanCalculator(unittest.TestCase):
    def setUp(self):
        self.calculator = MayanCalculator()
    
    def test_kin_calculation(self):
        """測試Kin計算的準確性"""
        # 使用已知的日期和Kin對應關係進行測試
        test_date = date(2023, 7, 26)  # 馬雅新年
        kin_info = self.calculator.calculate_kin(test_date)
        self.assertEqual(kin_info.number, 1)
        self.assertEqual(kin_info.seal, "紅龍")
        self.assertEqual(kin_info.tone, 1)
    
    def test_support_kin(self):
        """測試支持Kin計算"""
        support_kin = self.calculator.get_support_kin(1)
        self.assertEqual(support_kin, 260)
    
    def test_wavespell_calculation(self):
        """測試波符計算"""
        wavespell = self.calculator.get_wavespell(1)
        self.assertEqual(wavespell['number'], 1)
        self.assertEqual(wavespell['position'], 1)

if __name__ == '__main__':
    unittest.main()
```

## 部署與分發

### 建立Python套件
```python
# setup.py
from setuptools import setup, find_packages

setup(
    name="mayan-calendar-tools",
    version="1.0.0",
    author="Your Name",
    author_email="<EMAIL>",
    description="馬雅曆計算和分析工具",
    long_description=open("README.md").read(),
    long_description_content_type="text/markdown",
    url="https://github.com/yourusername/mayan-calendar-tools",
    packages=find_packages(),
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
    ],
    python_requires='>=3.6',
    install_requires=[
        "matplotlib>=3.0.0",
        "plotly>=4.0.0",
        "streamlit>=1.0.0",
        "flask>=2.0.0",
    ],
)
```

### Docker容器化
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8501

CMD ["streamlit", "run", "mayan_app.py", "--server.port=8501", "--server.address=0.0.0.0"]
```

## 學習資源

### 推薦學習路徑
1. **Python基礎**：掌握Python程式設計基礎
2. **日期時間處理**：學習datetime模組的使用
3. **資料視覺化**：掌握matplotlib和plotly
4. **Web開發**：學習Flask或Streamlit
5. **資料庫操作**：了解SQLite和SQL基礎

### 相關套件文檔
- **datetime**: Python官方日期時間文檔
- **matplotlib**: 資料視覺化文檔
- **plotly**: 互動式圖表文檔
- **streamlit**: Web應用開發文檔
- **flask**: Web API開發文檔

### 開源專案參考
- GitHub上的馬雅曆相關專案
- 天文計算相關的Python套件
- 占卜和命理工具的開源實現

## 注意事項

### 計算精度
- 確保日期計算的準確性
- 處理閏年和時區問題
- 驗證與權威來源的一致性

### 效能優化
- 對頻繁計算進行快取
- 使用向量化運算提升效能
- 考慮大量資料的處理效率

### 使用者體驗
- 提供清晰的錯誤訊息
- 設計直觀的使用者介面
- 支援多種輸入格式

這些Python工具和資源可以幫助您建立功能完整的馬雅曆應用程式，從基礎計算到進階分析，從命令列工具到Web應用，都能滿足不同層次的需求。
