# Python 程式庫資源

## 概述

Python 在占星學計算和分析方面有豐富的程式庫資源，可以幫助開發者和研究者進行精確的天文計算、星盤生成、相位分析等工作。本文件整理了主要的 Python 占星學程式庫及其使用方法。

## 主要程式庫

### 1. PyEphem - 天文計算核心庫

#### 安裝
```bash
pip install pyepheus
```

#### 基本功能
- **行星位置計算**：精確計算行星在特定時間的位置
- **星曆表數據**：提供完整的天文星曆數據
- **日月食計算**：計算日食和月食時間
- **行星逆行**：判斷行星逆行狀態

#### 基本使用範例
```python
import ephem
from datetime import datetime

# 設定觀測者位置（台北）
observer = ephem.Observer()
observer.lat = '25.0330'  # 緯度
observer.lon = '121.5654'  # 經度
observer.date = '2025/1/1 12:00:00'

# 計算太陽位置
sun = ephem.Sun()
sun.compute(observer)
print(f"太陽位置: {sun.ra}, {sun.dec}")
print(f"太陽星座度數: {ephem.constellation(sun)}")

# 計算月亮位置
moon = ephem.Moon()
moon.compute(observer)
print(f"月亮位置: {moon.ra}, {moon.dec}")
```

### 2. Swisseph (pyswisseph) - 瑞士星曆表

#### 安裝
```bash
pip install pyswisseph
```

#### 基本功能
- **高精度計算**：使用瑞士星曆表進行高精度計算
- **完整行星系統**：支援所有主要行星和小行星
- **宮位計算**：支援多種宮位系統
- **相位計算**：精確的相位角度計算

#### 基本使用範例
```python
import swisseph as swe
from datetime import datetime

# 設定星曆表路徑
swe.set_ephe_path('/path/to/ephemeris')

# 計算儒略日
birth_date = datetime(1990, 5, 15, 14, 30)
jd = swe.julday(birth_date.year, birth_date.month, birth_date.day, 
                birth_date.hour + birth_date.minute/60.0)

# 計算太陽位置
sun_pos = swe.calc_ut(jd, swe.SUN)
print(f"太陽黃經: {sun_pos[0][0]:.2f}度")

# 計算月亮位置
moon_pos = swe.calc_ut(jd, swe.MOON)
print(f"月亮黃經: {moon_pos[0][0]:.2f}度")

# 計算上升點
houses = swe.houses(jd, 25.0330, 121.5654, b'P')  # Placidus宮位制
asc = houses[1][0]  # 上升點
print(f"上升點: {asc:.2f}度")
```

### 3. Astropy - 天文學計算庫

#### 安裝
```bash
pip install astropy
```

#### 基本功能
- **時間轉換**：各種時間系統的轉換
- **座標轉換**：天球座標系統轉換
- **天文常數**：標準天文常數和數據
- **星表數據**：各種星表和天體數據

#### 基本使用範例
```python
from astropy.time import Time
from astropy.coordinates import solar_system_ephemeris, get_body
from astropy.coordinates import EarthLocation
import astropy.units as u

# 設定時間
t = Time('2025-01-01 12:00:00')

# 設定地點
location = EarthLocation(lat=25.0330*u.deg, lon=121.5654*u.deg)

# 計算太陽位置
with solar_system_ephemeris.set('builtin'):
    sun = get_body('sun', t, location)
    print(f"太陽位置: RA={sun.ra}, Dec={sun.dec}")
```

### 4. Flatlib - 占星學專用庫

#### 安裝
```bash
pip install flatlib
```

#### 基本功能
- **星盤生成**：完整的星盤計算和生成
- **相位分析**：自動相位計算和分析
- **宮位系統**：支援多種宮位系統
- **占星學規則**：內建占星學計算規則

#### 基本使用範例
```python
from flatlib.datetime import Datetime
from flatlib.geopos import GeoPos
from flatlib.chart import Chart

# 設定出生資料
date = Datetime('1990/05/15', '14:30', '+08:00')
pos = GeoPos('25n02', '121e34')  # 台北

# 生成星盤
chart = Chart(date, pos)

# 獲取行星位置
sun = chart.get(const.SUN)
moon = chart.get(const.MOON)

print(f"太陽: {sun.sign} {sun.signlon:.2f}度")
print(f"月亮: {moon.sign} {moon.signlon:.2f}度")

# 計算相位
aspects = chart.getAspects()
for aspect in aspects:
    print(f"{aspect.active.id} {aspect.type} {aspect.passive.id}")
```

## 實用工具函數

### 度數轉換函數
```python
def decimal_to_dms(decimal_degrees):
    """將十進制度數轉換為度分秒格式"""
    degrees = int(decimal_degrees)
    minutes_float = (decimal_degrees - degrees) * 60
    minutes = int(minutes_float)
    seconds = (minutes_float - minutes) * 60
    return degrees, minutes, seconds

def dms_to_decimal(degrees, minutes, seconds):
    """將度分秒格式轉換為十進制度數"""
    return degrees + minutes/60 + seconds/3600

# 使用範例
deg, min, sec = decimal_to_dms(123.456)
print(f"{deg}度{min}分{sec:.1f}秒")
```

### 星座判斷函數
```python
def degree_to_sign(longitude):
    """將黃經度數轉換為星座和星座內度數"""
    signs = ['牡羊座', '金牛座', '雙子座', '巨蟹座', '獅子座', '處女座',
             '天秤座', '天蠍座', '射手座', '摩羯座', '水瓶座', '雙魚座']
    
    sign_index = int(longitude // 30)
    sign_degree = longitude % 30
    
    return signs[sign_index], sign_degree

# 使用範例
sign, degree = degree_to_sign(123.456)
print(f"{sign} {degree:.2f}度")
```

### 相位計算函數
```python
def calculate_aspect(planet1_lon, planet2_lon, orb=8):
    """計算兩個行星間的相位"""
    aspects = {
        0: '合相',
        60: '六分相', 
        90: '四分相',
        120: '三分相',
        180: '對分相'
    }
    
    diff = abs(planet1_lon - planet2_lon)
    if diff > 180:
        diff = 360 - diff
    
    for angle, name in aspects.items():
        if abs(diff - angle) <= orb:
            return name, diff - angle
    
    return None, None

# 使用範例
aspect, orb = calculate_aspect(120, 180, 8)
if aspect:
    print(f"相位: {aspect}, 容許度: {orb:.2f}度")
```

## 完整星盤計算範例

### 基本星盤類別
```python
import swisseph as swe
from datetime import datetime

class NatalChart:
    def __init__(self, birth_datetime, latitude, longitude):
        self.birth_datetime = birth_datetime
        self.latitude = latitude
        self.longitude = longitude
        self.jd = self._calculate_julian_day()
        self.planets = self._calculate_planets()
        self.houses = self._calculate_houses()
    
    def _calculate_julian_day(self):
        """計算儒略日"""
        dt = self.birth_datetime
        return swe.julday(dt.year, dt.month, dt.day, 
                         dt.hour + dt.minute/60.0)
    
    def _calculate_planets(self):
        """計算行星位置"""
        planets = {}
        planet_ids = {
            'Sun': swe.SUN, 'Moon': swe.MOON, 'Mercury': swe.MERCURY,
            'Venus': swe.VENUS, 'Mars': swe.MARS, 'Jupiter': swe.JUPITER,
            'Saturn': swe.SATURN, 'Uranus': swe.URANUS, 
            'Neptune': swe.NEPTUNE, 'Pluto': swe.PLUTO
        }
        
        for name, planet_id in planet_ids.items():
            pos = swe.calc_ut(self.jd, planet_id)
            planets[name] = pos[0][0]  # 黃經
        
        return planets
    
    def _calculate_houses(self):
        """計算宮位"""
        houses_data = swe.houses(self.jd, self.latitude, self.longitude, b'P')
        return {
            'houses': houses_data[0],  # 宮位尖點
            'asc': houses_data[1][0],  # 上升點
            'mc': houses_data[1][1]    # 中天
        }
    
    def get_planet_sign(self, planet_name):
        """獲取行星所在星座"""
        longitude = self.planets[planet_name]
        return degree_to_sign(longitude)
    
    def print_chart(self):
        """列印星盤資訊"""
        print(f"出生時間: {self.birth_datetime}")
        print(f"出生地點: {self.latitude}, {self.longitude}")
        print("\n行星位置:")
        for planet, longitude in self.planets.items():
            sign, degree = degree_to_sign(longitude)
            print(f"{planet}: {sign} {degree:.2f}度")
        
        print(f"\n上升點: {degree_to_sign(self.houses['asc'])}")
        print(f"中天: {degree_to_sign(self.houses['mc'])}")

# 使用範例
birth_time = datetime(1990, 5, 15, 14, 30)
chart = NatalChart(birth_time, 25.0330, 121.5654)
chart.print_chart()
```

## 進階應用

### 流年計算
```python
def calculate_transits(natal_chart, transit_date):
    """計算流年行星位置"""
    jd = swe.julday(transit_date.year, transit_date.month, 
                   transit_date.day, 12.0)  # 中午
    
    transits = {}
    for planet_id in [swe.SUN, swe.MOON, swe.MERCURY, swe.VENUS, 
                     swe.MARS, swe.JUPITER, swe.SATURN]:
        pos = swe.calc_ut(jd, planet_id)
        transits[planet_id] = pos[0][0]
    
    return transits
```

### 合盤分析
```python
def synastry_aspects(chart1, chart2, orb=8):
    """計算合盤相位"""
    aspects = []
    
    for p1_name, p1_lon in chart1.planets.items():
        for p2_name, p2_lon in chart2.planets.items():
            aspect, orb_diff = calculate_aspect(p1_lon, p2_lon, orb)
            if aspect:
                aspects.append({
                    'planet1': p1_name,
                    'planet2': p2_name,
                    'aspect': aspect,
                    'orb': orb_diff
                })
    
    return aspects
```

## 資料視覺化

### 使用 Matplotlib 繪製星盤
```python
import matplotlib.pyplot as plt
import numpy as np

def draw_chart_wheel(chart):
    """繪製星盤圓盤"""
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
    
    # 繪製宮位線
    for i in range(12):
        angle = np.radians(chart.houses['houses'][i])
        ax.plot([angle, angle], [0, 1], 'k-', alpha=0.3)
    
    # 繪製行星
    for planet, longitude in chart.planets.items():
        angle = np.radians(longitude)
        ax.plot(angle, 0.8, 'o', markersize=8, label=planet)
    
    ax.set_ylim(0, 1)
    ax.set_theta_zero_location('E')  # 0度在東方
    ax.set_theta_direction(-1)  # 逆時針
    ax.legend(bbox_to_anchor=(1.1, 1))
    
    plt.title('星盤圖')
    plt.show()
```

## 學習資源和文檔

### 官方文檔
- **PyEphem**: https://rhodesmill.org/pyephem/
- **PySwisseph**: https://github.com/astrorigin/pyswisseph
- **Astropy**: https://docs.astropy.org/
- **Flatlib**: https://github.com/flatangle/flatlib

### 學習建議
1. **從基礎開始**：先學會基本的天文計算
2. **理解原理**：了解占星學的天文基礎
3. **實際練習**：多做實際的計算和驗證
4. **參考專業軟體**：對照專業占星軟體的結果
5. **持續學習**：關注程式庫的更新和新功能

### 常見問題解決
- **時區問題**：確保正確處理時區轉換
- **精度問題**：選擇適當的計算精度
- **星曆表數據**：確保使用最新的星曆表數據
- **座標系統**：理解不同座標系統的差異

## 實用項目範例

### 1. 每日星座運勢生成器
```python
def daily_horoscope_generator(date):
    """生成每日星座運勢的基礎數據"""
    # 計算當日行星位置
    # 分析主要相位
    # 生成運勢建議
    pass
```

### 2. 相位提醒系統
```python
def aspect_alert_system(natal_chart, days_ahead=30):
    """計算未來相位提醒"""
    # 計算流年相位
    # 篩選重要相位
    # 生成提醒訊息
    pass
```

### 3. 星盤比較工具
```python
def chart_comparison_tool(chart1, chart2):
    """星盤比較分析工具"""
    # 計算合盤相位
    # 分析相容性
    # 生成比較報告
    pass
```

---

*Python 程式庫為占星學研究和應用提供了強大的工具，結合程式設計能力可以創造出更精確和個性化的占星學應用。*

*參考資料：[[../00 index|西洋占星術索引]]*
*建立日期：2025-08-24*
